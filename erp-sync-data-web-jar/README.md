# ERP数据同步系统

## 项目概述

这是一个企业级的ERP数据同步系统，用于在不同系统之间同步数据。系统支持多种数据源和目标，包括ERP系统、CRM系统等。

## 核心功能

### 1. 历史数据任务管理
- **AbstractErpHistoryTaskServiceImpl**: 抽象的历史任务服务实现类，提供了历史数据同步任务的核心功能
- 支持按时间、按ID、按条件等多种方式同步历史数据
- 提供任务创建、编辑、执行等完整的生命周期管理
- 支持重复数据检测和处理
- 提供任务限制和配额管理

### 2. 数据同步类型
- **TYPE_TIME**: 按照时间范围同步
- **TYPE_IDS**: 按照指定ID列表同步
- **TYPE_K3C_FILTER_STRING**: 按照K3自定义条件同步
- **TYPE_CRM_FILTER**: 按照CRM数据范围同步
- **TYPE_TIME_INVALID**: 按照时间查询作废数据
- **TYPE_FILE_IDS**: 通过文件导入ID列表同步

### 3. 任务状态管理
- **STATUS_WAITING**: 等待执行
- **STATUS_START**: 开始执行
- **STATUS_EXECUTING**: 执行中
- **STATUS_ERROR**: 执行异常
- **STATUS_STOP**: 用户停止
- **STATUS_END_SUCCESS**: 执行成功

## 单元测试

### AbstractErpHistoryTaskServiceImpl 测试覆盖

我们为 `AbstractErpHistoryTaskServiceImpl` 类创建了完整的单元测试，覆盖了以下功能：

#### 测试覆盖的方法：
1. **createHistoryTask** - 创建历史任务
   - 测试成功创建多个任务
   - 验证DAO调用和日志记录

2. **editHistorySnapshotTask** - 编辑历史快照任务
   - 测试成功编辑任务
   - 验证数据库更新和日志更新

3. **createErpHistoryDataTask** - 创建ERP历史数据任务
   - 参数验证测试（空值、null值）
   - ERP类型和非ERP类型处理
   - 对象关系查询测试
   - 限制检查测试
   - 保存成功/失败场景

4. **editErpHistoryDataTask** - 编辑ERP历史数据任务
   - 参数验证测试
   - 任务存在性检查
   - 任务状态验证
   - 编辑成功/失败场景

5. **incDuplicateTime** - 重复数据处理
   - 成功处理重复数据
   - 空列表处理
   - 已存在数据更新
   - 新数据创建

6. **checkLimit** - 限制检查（私有方法通过反射测试）
   - 秘钥绕过测试
   - 未结束任务数量限制
   - 每日创建任务数量限制
   - 正常通过检查

#### 测试技术栈：
- **JUnit 5**: 主要测试框架
- **Mockito**: Mock框架，用于模拟依赖
- **Spring Test**: Spring测试支持
- **ReflectionTestUtils**: 用于测试私有方法

#### 运行测试：

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=AbstractErpHistoryTaskServiceImplTest

# 运行测试并生成覆盖率报告
mvn clean test jacoco:report
```

#### 手动运行测试：
```bash
# 进入项目目录
cd erp-preprocess-data

# 编译并运行测试运行器
mvn compile test-compile
java -cp "target/test-classes:target/classes:$(mvn dependency:build-classpath -Dmdep.outputFile=/dev/stdout -q)" com.fxiaoke.open.erpsyncdata.preprocess.impl.task.TestRunner
```

#### 测试文件位置：
- 测试类: `erp-preprocess-data/src/test/java/com/fxiaoke/open/erpsyncdata/preprocess/impl/task/AbstractErpHistoryTaskServiceImplTest.java`
- 测试配置: `erp-preprocess-data/src/test/resources/application-test.properties`
- 测试运行器: `erp-preprocess-data/src/test/java/com/fxiaoke/open/erpsyncdata/preprocess/impl/task/TestRunner.java`

#### 测试覆盖率：
测试用例覆盖了以下场景：
- ✅ 正常流程测试
- ✅ 异常情况处理
- ✅ 边界条件验证
- ✅ 参数验证
- ✅ Mock对象交互验证
- ✅ 私有方法测试（通过反射）

## 管理工具说明

### 使用在线编辑器编辑、调试管理页面

1. 启动web项目，配置jvm参数`-Derpdss.skip.super.auth=true`,用于本地跳过superAdmin校验
2. 改造想要修改的json文件
   1. 第一级增加data，设置baseUrl为`erp/syncdata/superadmin`。最后复制回项目里面时，再修改回`..`
   2. 将json内的url上所有的`..`都修改为`${baseUrl}`
3. 下载amis-editor-demo项目 https://github.com/aisuda/amis-editor-demo
4. 修改`amis.config.js`文件的`proxyTable`如下：
   ```javascript
    proxyTable: {
    /**
    * 将含有'/apiTest'路径的api代理到'http://api-test.com.cn'上，
    * 详细使用见 https://www.webpackjs.com/configuration/dev-server/#devserver-proxy
    */
    '/erp/syncdata': {
    target: 'http://localhost:8084', // 不支持跨域的接口根地址
    ws: true,
    changeOrigin: true,
    },
    }
   ```
5. 执行`npm i`和`npm run dev`
6. 进入编辑器后，新建或编辑页面
7. 左边选择源码，把当前项目想要修改的页面json，粘贴进去
8. 尽情修改吧！！！ `(*^▽^*)`