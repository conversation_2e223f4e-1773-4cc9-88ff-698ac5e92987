## 管理工具说明

### 使用在线编辑器编辑、调试管理页面

1. 启动web项目，配置jvm参数`-Derpdss.skip.super.auth=true`,用于本地跳过superAdmin校验
2. 改造想要修改的json文件
   1. 第一级增加data，设置baseUrl为`erp/syncdata/superadmin`。最后复制回项目里面时，再修改回`..`
   2. 将json内的url上所有的`..`都修改为`${baseUrl}`
3. 下载amis-editor-demo项目 https://github.com/aisuda/amis-editor-demo
4. 修改`amis.config.js`文件的`proxyTable`如下：
   ```javascript
    proxyTable: {
    /**
    * 将含有'/apiTest'路径的api代理到'http://api-test.com.cn'上，
    * 详细使用见 https://www.webpackjs.com/configuration/dev-server/#devserver-proxy
    */
    '/erp/syncdata': {
    target: 'http://localhost:8084', // 不支持跨域的接口根地址
    ws: true,
    changeOrigin: true,
    },
    }
   ```
5. 执行`npm i`和`npm run dev`
6. 进入编辑器后，新建或编辑页面
7. 左边选择源码，把当前项目想要修改的页面json，粘贴进去
8. 尽情修改吧！！！ `(*^▽^*)`

## 单元测试

### CrmFilterErpIdsHistoryTaskServiceImpl 测试覆盖

我们为 `CrmFilterErpIdsHistoryTaskServiceImpl` 类创建了完整的单元测试，覆盖了以下功能：

#### 测试覆盖的方法：
1. **taskType** - 返回任务类型
   - 验证返回正确的枚举值

2. **saveTaskSuccess** - 保存任务成功处理
   - 测试成功保存场景
   - 验证CRM过滤器设置

3. **editTaskSuccess** - 编辑任务成功处理
   - 测试成功编辑场景
   - 验证任务更新流程

4. **afterConvert2Vo** - 转换后处理
   - 验证dataIds字段被正确清空
   - 测试父类方法调用

5. **doTask** - 执行任务核心逻辑
   - 成功执行场景
   - ERP服务错误处理
   - 无ERP ID找到的场景
   - 非创建状态映射过滤
   - 重复ERP ID去重处理
   - 任务备注错误信息更新
   - 混合映射场景测试

6. **getNeedSyncErpIds** - 获取需要同步的ERP ID（私有方法通过doTask测试）
   - 有效映射过滤
   - 创建状态检查
   - 重复ID去重
   - 空映射处理

#### 测试技术栈：
- **JUnit 5**: 主要测试框架
- **Mockito**: Mock框架，用于模拟依赖
- **Spring Test**: Spring测试支持
- **ReflectionTestUtils**: 用于注入私有字段

#### 测试文件位置：
- 测试类: `erp-preprocess-data/src/test/java/com/fxiaoke/open/erpsyncdata/preprocess/impl/task/CrmFilterErpIdsHistoryTaskServiceImplTest.java`
- 测试运行器: `erp-preprocess-data/src/test/java/com/fxiaoke/open/erpsyncdata/preprocess/impl/task/CrmFilterErpIdsTestRunner.java`

#### 运行测试：

```bash
# 运行CrmFilterErpIdsHistoryTaskServiceImpl测试
mvn test -Dtest=CrmFilterErpIdsHistoryTaskServiceImplTest

# 手动运行测试运行器
java -cp "target/test-classes:target/classes:$(mvn dependency:build-classpath -Dmdep.outputFile=/dev/stdout -q)" com.fxiaoke.open.erpsyncdata.preprocess.impl.task.CrmFilterErpIdsTestRunner
```

#### 测试覆盖率：
测试用例覆盖了以下场景：
- ✅ 正常流程测试
- ✅ 异常情况处理
- ✅ 边界条件验证
- ✅ 参数验证
- ✅ Mock对象交互验证
- ✅ 私有方法测试（通过公共方法）
- ✅ 继承方法测试
- ✅ 数据映射和过滤逻辑
- ✅ ERP服务集成测试
- ✅ 错误处理和日志记录

### AbstractErpHistoryTaskServiceImpl 测试覆盖

我们也为抽象基类 `AbstractErpHistoryTaskServiceImpl` 创建了单元测试：

#### 测试文件位置：
- 完整测试类: `erp-preprocess-data/src/test/java/com/fxiaoke/open/erpsyncdata/preprocess/impl/task/AbstractErpHistoryTaskServiceImplTest.java`
- 简化测试类: `erp-preprocess-data/src/test/java/com/fxiaoke/open/erpsyncdata/preprocess/impl/task/AbstractErpHistoryTaskServiceImplSimpleTest.java`

#### 运行所有测试：

```bash
# 运行所有测试
mvn test

# 运行特定包下的测试
mvn test -Dtest="com.fxiaoke.open.erpsyncdata.preprocess.impl.task.*Test"

# 生成测试报告
mvn clean test jacoco:report
```