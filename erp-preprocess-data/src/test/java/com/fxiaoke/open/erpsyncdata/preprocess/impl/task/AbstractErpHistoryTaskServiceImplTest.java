package com.fxiaoke.open.erpsyncdata.preprocess.impl.task;

import cn.hutool.core.util.StrUtil;
import com.fxiaoke.open.erpsyncdata.dbproxy.constant.CommonConstant;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpHistoryDataTaskDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.dao.ErpObjectRelationshipDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectRelationshipEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.data.ListStringData;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.TenantConfigurationManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpHistoryTaskLogDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.HistoryTaskDuplicateDataDao;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.ErpHistoryTaskLog;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.document.HistoryTaskDuplicateDataEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.redis.RedisDataSource;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringEnum;
import com.fxiaoke.open.erpsyncdata.i18n.I18NStringManager;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.CreateErpHistoryDataTaskArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.*;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.IntegrationSimpleViewInfoResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.google.common.collect.Lists;
import com.alibaba.fastjson.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AbstractErpHistoryTaskServiceImpl 单元测试类
 *
 * <AUTHOR> Assistant
 * @date 2025/06/25
 */
@ExtendWith(MockitoExtension.class)
class AbstractErpHistoryTaskServiceImplTest {

    @Mock
    private I18NStringManager i18NStringManager;

    @Mock
    private ErpObjectRelationshipDao erpObjectRelationshipDao;

    @Mock
    private TenantConfigurationManager tenantConfigurationManager;

    @Mock
    private ErpHistoryDataTaskDao erpHistoryDataTaskDao;

    @Mock
    private ErpHistoryTaskLogDao erpHistoryTaskLogDao;

    @Mock
    private HistoryTaskDuplicateDataDao historyTaskDuplicateDataDao;

    @Mock
    private RedisDataSource redisDataSource;

    @InjectMocks
    private TestableAbstractErpHistoryTaskServiceImpl testService;

    private static final String TEST_TENANT_ID = "test_tenant_123";
    private static final String TEST_DC_ID = "test_dc_456";
    private static final Integer TEST_USER_ID = 1001;
    private static final String TEST_LANG = "zh_CN";

    @BeforeEach
    void setUp() {
        // 注入私有字段
        ReflectionTestUtils.setField(testService, "i18NStringManager", i18NStringManager);
        ReflectionTestUtils.setField(testService, "erpObjectRelationshipDao", erpObjectRelationshipDao);
        ReflectionTestUtils.setField(testService, "tenantConfigurationManager", tenantConfigurationManager);
        ReflectionTestUtils.setField(testService, "erpHistoryDataTaskDao", erpHistoryDataTaskDao);
        ReflectionTestUtils.setField(testService, "erpHistoryTaskLogDao", erpHistoryTaskLogDao);
        ReflectionTestUtils.setField(testService, "historyTaskDuplicateDataDao", historyTaskDuplicateDataDao);
        ReflectionTestUtils.setField(testService, "redisDataSource", redisDataSource);
    }

    /**
     * 测试创建历史任务 - 成功场景
     */
    @Test
    void testCreateHistoryTask_Success() {
        // 准备测试数据
        ErpHistoryDataTaskEntity entity1 = createTestEntity("task1");
        ErpHistoryDataTaskEntity entity2 = createTestEntity("task2");
        List<ErpHistoryDataTaskEntity> entities = Arrays.asList(entity1, entity2);

        // Mock DAO 行为
        when(erpHistoryDataTaskDao.setTenantId(anyString())).thenReturn(erpHistoryDataTaskDao);
        when(erpHistoryDataTaskDao.batchInsert(anyList())).thenReturn(true);
        when(erpHistoryTaskLogDao.batchInsert(eq(TEST_TENANT_ID), anyList())).thenReturn(true);

        // 执行测试
        boolean result = testService.createHistoryTask(TEST_TENANT_ID, entities);

        // 验证结果
        assertTrue(result);
        verify(erpHistoryDataTaskDao).setTenantId(anyString());
        verify(erpHistoryDataTaskDao).batchInsert(entities);
        verify(erpHistoryTaskLogDao).batchInsert(eq(TEST_TENANT_ID), anyList());
    }

    /**
     * 测试编辑历史快照任务 - 成功场景
     */
    @Test
    void testEditHistorySnapshotTask_Success() {
        // 准备测试数据
        ErpHistoryDataTaskEntity entity = createTestEntity("edit_task");

        // Mock DAO 行为
        when(erpHistoryDataTaskDao.setTenantId(anyString())).thenReturn(erpHistoryDataTaskDao);
        when(erpHistoryDataTaskDao.updateEntityById(any())).thenReturn(true);
        when(erpHistoryTaskLogDao.updateLastErpHistoryByTaskId(anyString(), anyString(), anyString(), any()))
                .thenReturn(true);

        // 执行测试
        boolean result = testService.editHistorySnapshotTask(TEST_TENANT_ID, entity);

        // 验证结果
        assertTrue(result);
        verify(erpHistoryDataTaskDao).setTenantId(anyString());
        verify(erpHistoryDataTaskDao).updateEntityById(entity);
        verify(erpHistoryTaskLogDao).updateLastErpHistoryByTaskId(eq(TEST_TENANT_ID), eq(entity.getDataCenterId()),
                eq(entity.getId()), any(ErpHistoryTaskLog.class));
    }

    /**
     * 测试创建ERP历史数据任务 - 参数错误场景
     */
    @Test
    void testCreateErpHistoryDataTask_ParamError() {
        // 测试空的objApiName
        CreateErpHistoryDataTaskArg arg = createTestArg();
        arg.getTask().setObjApiName("");

        Result<String> result = testService.createErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.PARAM_ERROR.getErrCode(), result.getErrCode());

        // 测试空的taskName
        arg = createTestArg();
        arg.getTask().setTaskName("");

        result = testService.createErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.PARAM_ERROR.getErrCode(), result.getErrCode());

        // 测试空的remark
        arg = createTestArg();
        arg.getTask().setRemark("");

        result = testService.createErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.PARAM_ERROR.getErrCode(), result.getErrCode());

        // 测试空的taskType
        arg = createTestArg();
        arg.getTask().setTaskType(null);

        result = testService.createErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.PARAM_ERROR.getErrCode(), result.getErrCode());

        // 测试空的executeTime
        arg = createTestArg();
        arg.getTask().setExecuteTime(null);

        result = testService.createErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.PARAM_ERROR.getErrCode(), result.getErrCode());
    }

    /**
     * 测试创建ERP历史数据任务 - 检查参数错误
     */
    @Test
    void testCreateErpHistoryDataTask_CheckParamsError() {
        CreateErpHistoryDataTaskArg arg = createTestArg();

        // Mock checkParamsError 返回 true
        testService.setCheckParamsErrorResult(true);

        Result<String> result = testService.createErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.PARAM_ERROR.getErrCode(), result.getErrCode());
    }

    /**
     * 测试创建ERP历史数据任务 - 超出限制
     */
    @Test
    void testCreateErpHistoryDataTask_ExceedLimit() {
        CreateErpHistoryDataTaskArg arg = createTestArg();

        // Mock 超出限制
        when(erpHistoryDataTaskDao.countByStatus(eq(TEST_TENANT_ID), any())).thenReturn(15);
        when(i18NStringManager.getText(any())).thenReturn("超出限制");

        Result<String> result = testService.createErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.ERROR_MSG.getErrCode(), result.getErrCode());
    }

    /**
     * 创建测试用的 ErpHistoryDataTaskEntity
     */
    private ErpHistoryDataTaskEntity createTestEntity(String taskId) {
        ErpHistoryDataTaskEntity entity = new ErpHistoryDataTaskEntity();
        entity.setId(taskId);
        entity.setTenantId(TEST_TENANT_ID);
        entity.setDataCenterId(TEST_DC_ID);
        entity.setTaskName("Test Task");
        entity.setObjApiName("test_obj");
        entity.setRealObjApiName("real_test_obj");
        entity.setTaskType(ErpHistoryDataTaskTypeEnum.TYPE_IDS.getStatus());
        entity.setTaskStatus(ErpHistoryDataTaskStatusEnum.STATUS_WAITING.getStatus());
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());
        entity.setExecuteTime(System.currentTimeMillis() + 3600000); // 1小时后执行
        return entity;
    }

    /**
     * 创建测试用的 CreateErpHistoryDataTaskArg
     */
    private CreateErpHistoryDataTaskArg createTestArg() {
        CreateErpHistoryDataTaskArg arg = new CreateErpHistoryDataTaskArg();
        arg.setIsCheck(true);

        ErpHistoryDataTaskResult task = new ErpHistoryDataTaskResult();
        task.setObjApiName("test_obj");
        task.setTaskName("Test Task");
        task.setRemark("Test Remark");
        task.setTaskType(ErpHistoryDataTaskTypeEnum.TYPE_IDS.getStatus());
        task.setExecuteTime(System.currentTimeMillis() + 3600000);
        task.setPriority(1);

        // 添加集成结果
        IntegrationSimpleViewInfoResult integration = new IntegrationSimpleViewInfoResult();
        integration.setId("integration_1");
        task.setIntegrationResults(Arrays.asList(integration));

        arg.setTask(task);
        return arg;
    }

    /**
     * 测试创建ERP历史数据任务 - 成功场景（ERP类型）
     */
    @Test
    void testCreateErpHistoryDataTask_Success_ErpType() {
        CreateErpHistoryDataTaskArg arg = createTestArg();
        arg.getTask().setTaskType(ErpHistoryDataTaskTypeEnum.TYPE_IDS.getStatus());

        // Mock 对象关系查询
        ErpObjectRelationshipEntity relationshipEntity = new ErpObjectRelationshipEntity();
        relationshipEntity.setErpRealObjectApiname("real_obj_api");

        when(erpObjectRelationshipDao.setTenantId(anyString())).thenReturn(erpObjectRelationshipDao);
        when(erpObjectRelationshipDao.findBySplit(eq(TEST_TENANT_ID), eq("test_obj"))).thenReturn(relationshipEntity);

        // Mock 限制检查
        when(erpHistoryDataTaskDao.countByStatus(eq(TEST_TENANT_ID), any())).thenReturn(5);
        when(redisDataSource.incrAndExpire(anyString(), anyLong(), anyLong(), anyString())).thenReturn(50L);

        // Mock 保存成功
        testService.setSaveTaskSuccessResult(true);

        Result<String> result = testService.createErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.SUCCESS.getErrCode(), result.getErrCode());
        verify(erpObjectRelationshipDao).findBySplit(TEST_TENANT_ID, "test_obj");
    }

    /**
     * 测试创建ERP历史数据任务 - 对象关系不存在
     */
    @Test
    void testCreateErpHistoryDataTask_ObjectRelationshipNotFound() {
        CreateErpHistoryDataTaskArg arg = createTestArg();
        arg.getTask().setTaskType(ErpHistoryDataTaskTypeEnum.TYPE_IDS.getStatus());

        when(erpObjectRelationshipDao.setTenantId(anyString())).thenReturn(erpObjectRelationshipDao);
        when(erpObjectRelationshipDao.findBySplit(eq(TEST_TENANT_ID), eq("test_obj"))).thenReturn(null);

        Result<String> result = testService.createErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.PARAM_ERROR.getErrCode(), result.getErrCode());
    }

    /**
     * 测试创建ERP历史数据任务 - 成功场景（非ERP类型）
     */
    @Test
    void testCreateErpHistoryDataTask_Success_NonErpType() {
        CreateErpHistoryDataTaskArg arg = createTestArg();
        arg.getTask().setTaskType(ErpHistoryDataTaskTypeEnum.TYPE_CRM_FILTER.getStatus());

        // Mock 限制检查
        when(erpHistoryDataTaskDao.countByStatus(eq(TEST_TENANT_ID), any())).thenReturn(5);
        when(redisDataSource.incrAndExpire(anyString(), anyLong(), anyLong(), anyString())).thenReturn(50L);

        // Mock 保存成功
        testService.setSaveTaskSuccessResult(true);

        Result<String> result = testService.createErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.SUCCESS.getErrCode(), result.getErrCode());
        // 验证没有调用对象关系查询
        verify(erpObjectRelationshipDao, never()).findBySplit(anyString(), anyString());
    }

    /**
     * 测试创建ERP历史数据任务 - 保存失败
     */
    @Test
    void testCreateErpHistoryDataTask_SaveFailed() {
        CreateErpHistoryDataTaskArg arg = createTestArg();
        arg.getTask().setTaskType(ErpHistoryDataTaskTypeEnum.TYPE_CRM_FILTER.getStatus());

        // Mock 限制检查通过
        when(erpHistoryDataTaskDao.countByStatus(eq(TEST_TENANT_ID), any())).thenReturn(5);
        when(redisDataSource.incrAndExpire(anyString(), anyLong(), anyLong(), anyString())).thenReturn(50L);

        // Mock 保存失败
        testService.setSaveTaskSuccessResult(false);

        Result<String> result = testService.createErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), result.getErrCode());
    }

    /**
     * 测试编辑ERP历史数据任务 - 参数错误场景
     */
    @Test
    void testEditErpHistoryDataTask_ParamError() {
        CreateErpHistoryDataTaskArg arg = createTestArg();
        arg.getTask().setId("task_123");

        // 测试空的objApiName
        arg.getTask().setObjApiName("");

        Result<String> result = testService.editErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.PARAM_ERROR.getErrCode(), result.getErrCode());
    }

    /**
     * 测试编辑ERP历史数据任务 - 任务不存在
     */
    @Test
    void testEditErpHistoryDataTask_TaskNotFound() {
        CreateErpHistoryDataTaskArg arg = createTestArg();
        arg.getTask().setId("task_123");

        when(erpHistoryDataTaskDao.setTenantId(anyString())).thenReturn(erpHistoryDataTaskDao);
        when(erpHistoryDataTaskDao.findById("task_123")).thenReturn(null);

        Result<String> result = testService.editErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.TASK_NOT_SUPPORT_EDIT.getErrCode(), result.getErrCode());
    }

    /**
     * 测试编辑ERP历史数据任务 - 任务状态不支持编辑
     */
    @Test
    void testEditErpHistoryDataTask_TaskStatusNotSupported() {
        CreateErpHistoryDataTaskArg arg = createTestArg();
        arg.getTask().setId("task_123");

        ErpHistoryDataTaskEntity existingTask = createTestEntity("task_123");
        existingTask.setTaskStatus(ErpHistoryDataTaskStatusEnum.STATUS_EXECUTING.getStatus()); // 执行中状态不支持编辑

        when(erpHistoryDataTaskDao.setTenantId(anyString())).thenReturn(erpHistoryDataTaskDao);
        when(erpHistoryDataTaskDao.findById("task_123")).thenReturn(existingTask);

        Result<String> result = testService.editErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.TASK_NOT_SUPPORT_EDIT.getErrCode(), result.getErrCode());
    }

    /**
     * 测试编辑ERP历史数据任务 - 成功场景
     */
    @Test
    void testEditErpHistoryDataTask_Success() {
        CreateErpHistoryDataTaskArg arg = createTestArg();
        arg.getTask().setId("task_123");

        ErpHistoryDataTaskEntity existingTask = createTestEntity("task_123");
        existingTask.setTaskStatus(ErpHistoryDataTaskStatusEnum.STATUS_WAITING.getStatus()); // 等待状态支持编辑

        when(erpHistoryDataTaskDao.setTenantId(anyString())).thenReturn(erpHistoryDataTaskDao);
        when(erpHistoryDataTaskDao.findById("task_123")).thenReturn(existingTask);

        // Mock 编辑成功
        testService.setEditTaskSuccessResult(true);

        Result<String> result = testService.editErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.SUCCESS.getErrCode(), result.getErrCode());
    }

    /**
     * 测试编辑ERP历史数据任务 - 编辑失败
     */
    @Test
    void testEditErpHistoryDataTask_EditFailed() {
        CreateErpHistoryDataTaskArg arg = createTestArg();
        arg.getTask().setId("task_123");

        ErpHistoryDataTaskEntity existingTask = createTestEntity("task_123");
        existingTask.setTaskStatus(ErpHistoryDataTaskStatusEnum.STATUS_WAITING.getStatus());

        when(erpHistoryDataTaskDao.setTenantId(anyString())).thenReturn(erpHistoryDataTaskDao);
        when(erpHistoryDataTaskDao.findById("task_123")).thenReturn(existingTask);

        // Mock 编辑失败
        testService.setEditTaskSuccessResult(false);

        Result<String> result = testService.editErpHistoryDataTask(TEST_TENANT_ID, TEST_DC_ID, TEST_USER_ID, arg, TEST_LANG);

        assertEquals(ResultCodeEnum.SYSTEM_ERROR.getErrCode(), result.getErrCode());
    }

    /**
     * 测试增加重复次数 - 成功场景
     */
    @Test
    void testIncDuplicateTime_Success() {
        // 准备测试数据
        List<SyncDataContextEvent> events = createTestSyncDataEvents();
        String taskNum = "TASK_123456789";

        // Mock 查询已存在的数据
        Set<String> existingIds = new HashSet<>(Arrays.asList("erp_id_1"));
        when(historyTaskDuplicateDataDao.findExists(eq(TEST_TENANT_ID), eq(taskNum), anyList()))
                .thenReturn(existingIds);

        // Mock 增加重复次数
        when(historyTaskDuplicateDataDao.incDuplicateTime(eq(TEST_TENANT_ID), eq(taskNum), anyList(), anyInt()))
                .thenReturn(true);

        // Mock 创建新记录
        when(historyTaskDuplicateDataDao.create(anyList())).thenReturn(true);

        // 执行测试
        testService.incDuplicateTime(TEST_TENANT_ID, taskNum, events);

        // 验证调用
        verify(historyTaskDuplicateDataDao).findExists(eq(TEST_TENANT_ID), eq(taskNum), anyList());
        verify(historyTaskDuplicateDataDao).incDuplicateTime(eq(TEST_TENANT_ID), eq(taskNum), anyList(), anyInt());
        verify(historyTaskDuplicateDataDao).create(anyList());
    }

    /**
     * 测试根据ERP ID增加重复次数 - 空列表
     */
    @Test
    void testIncDuplicateTimeByErpIds_EmptyList() {
        List<String> emptyIds = new ArrayList<>();

        // 执行测试
        testService.incDuplicateTimeByErpIds(TEST_TENANT_ID, "TASK_123", emptyIds);

        // 验证没有调用DAO方法
        verify(historyTaskDuplicateDataDao, never()).findExists(anyString(), anyString(), anyList());
        verify(historyTaskDuplicateDataDao, never()).incDuplicateTime(anyString(), anyString(), anyList(), anyInt());
        verify(historyTaskDuplicateDataDao, never()).create(anyList());
    }

    /**
     * 测试根据ERP ID增加重复次数 - 成功场景
     */
    @Test
    void testIncDuplicateTimeByErpIds_Success() {
        List<String> erpIds = Arrays.asList("erp_id_1", "erp_id_2", "erp_id_1", "erp_id_3");
        String taskNum = "TASK_123456789";

        // Mock 查询已存在的数据
        Set<String> existingIds = new HashSet<>(Arrays.asList("erp_id_1"));
        when(historyTaskDuplicateDataDao.findExists(eq(TEST_TENANT_ID), eq(taskNum), anyList()))
                .thenReturn(existingIds);

        // Mock 增加重复次数
        when(historyTaskDuplicateDataDao.incDuplicateTime(eq(TEST_TENANT_ID), eq(taskNum), anyList(), anyInt()))
                .thenReturn(true);

        // Mock 创建新记录
        when(historyTaskDuplicateDataDao.create(anyList())).thenReturn(true);

        // 执行测试
        testService.incDuplicateTimeByErpIds(TEST_TENANT_ID, taskNum, erpIds);

        // 验证调用
        verify(historyTaskDuplicateDataDao).findExists(eq(TEST_TENANT_ID), eq(taskNum), anyList());
        verify(historyTaskDuplicateDataDao).incDuplicateTime(eq(TEST_TENANT_ID), eq(taskNum), anyList(), eq(2));
        verify(historyTaskDuplicateDataDao).create(anyList());
    }

    /**
     * 测试根据ERP ID增加重复次数 - 全部已存在
     */
    @Test
    void testIncDuplicateTimeByErpIds_AllExisting() {
        List<String> erpIds = Arrays.asList("erp_id_1", "erp_id_2");
        String taskNum = "TASK_123456789";

        // Mock 查询已存在的数据 - 全部都存在
        Set<String> existingIds = new HashSet<>(Arrays.asList("erp_id_1", "erp_id_2"));
        when(historyTaskDuplicateDataDao.findExists(eq(TEST_TENANT_ID), eq(taskNum), anyList()))
                .thenReturn(existingIds);

        // Mock 增加重复次数
        when(historyTaskDuplicateDataDao.incDuplicateTime(eq(TEST_TENANT_ID), eq(taskNum), anyList(), anyInt()))
                .thenReturn(true);

        // 执行测试
        testService.incDuplicateTimeByErpIds(TEST_TENANT_ID, taskNum, erpIds);

        // 验证调用
        verify(historyTaskDuplicateDataDao).findExists(eq(TEST_TENANT_ID), eq(taskNum), anyList());
        verify(historyTaskDuplicateDataDao).incDuplicateTime(eq(TEST_TENANT_ID), eq(taskNum), anyList(), eq(1));
        // 验证没有创建新记录
        verify(historyTaskDuplicateDataDao, never()).create(anyList());
    }

    /**
     * 测试限制检查 - 使用秘钥绕过
     */
    @Test
    void testCheckLimit_BypassWithSecret() throws Exception {
        CreateErpHistoryDataTaskArg arg = createTestArg();
        arg.setSkipLimitCheckSecret("!@#$");

        // 使用反射调用私有方法
        String result = (String) ReflectionTestUtils.invokeMethod(testService, "checkLimit", TEST_TENANT_ID, arg);

        assertNull(result);
        // 验证没有调用限制检查
        verify(erpHistoryDataTaskDao, never()).countByStatus(anyString(), any());
        verify(redisDataSource, never()).incrAndExpire(anyString(), anyLong(), anyLong(), anyString());
    }

    /**
     * 测试限制检查 - 未结束任务过多
     */
    @Test
    void testCheckLimit_TooManyNotEndTasks() throws Exception {
        CreateErpHistoryDataTaskArg arg = createTestArg();

        // Mock 未结束任务数量超限
        when(erpHistoryDataTaskDao.countByStatus(eq(TEST_TENANT_ID), any())).thenReturn(15);
        when(i18NStringManager.getText(I18NStringEnum.kNotEndTaskExcessive)).thenReturn("未结束任务过多");

        // 使用反射调用私有方法
        String result = (String) ReflectionTestUtils.invokeMethod(testService, "checkLimit", TEST_TENANT_ID, arg);

        assertEquals("未结束任务过多", result);
    }

    /**
     * 测试限制检查 - 每日创建任务过多
     */
    @Test
    void testCheckLimit_TooManyDailyTasks() throws Exception {
        CreateErpHistoryDataTaskArg arg = createTestArg();

        // Mock 未结束任务数量正常
        when(erpHistoryDataTaskDao.countByStatus(eq(TEST_TENANT_ID), any())).thenReturn(5);
        // Mock 每日创建任务数量超限
        when(redisDataSource.incrAndExpire(anyString(), anyLong(), anyLong(), anyString())).thenReturn(150L);
        when(i18NStringManager.getText(I18NStringEnum.kDailyTaskCreateExcessive)).thenReturn("每日创建任务过多");

        // 使用反射调用私有方法
        String result = (String) ReflectionTestUtils.invokeMethod(testService, "checkLimit", TEST_TENANT_ID, arg);

        assertEquals("每日创建任务过多", result);
    }

    /**
     * 测试限制检查 - 通过检查
     */
    @Test
    void testCheckLimit_Pass() throws Exception {
        CreateErpHistoryDataTaskArg arg = createTestArg();

        // Mock 限制检查通过
        when(erpHistoryDataTaskDao.countByStatus(eq(TEST_TENANT_ID), any())).thenReturn(5);
        when(redisDataSource.incrAndExpire(anyString(), anyLong(), anyLong(), anyString())).thenReturn(50L);

        // 使用反射调用私有方法
        String result = (String) ReflectionTestUtils.invokeMethod(testService, "checkLimit", TEST_TENANT_ID, arg);

        assertNull(result);
    }

    /**
     * 测试afterConvert2Vo方法 - 空实现
     */
    @Test
    void testAfterConvert2Vo() {
        ErpHistoryDataTaskResult result = new ErpHistoryDataTaskResult();
        ErpHistoryDataTaskEntity entity = createTestEntity("test");

        // 执行测试 - 应该不抛出异常
        assertDoesNotThrow(() -> testService.afterConvert2Vo(result, entity));
    }

    /**
     * 创建测试用的 SyncDataContextEvent 列表
     */
    private List<SyncDataContextEvent> createTestSyncDataEvents() {
        List<SyncDataContextEvent> events = new ArrayList<>();

        // 创建第一个事件
        SyncDataContextEvent event1 = new SyncDataContextEvent();
        JSONObject sourceData1 = new JSONObject();
        sourceData1.put("erp_id", "erp_id_1");
        event1.setSourceData(sourceData1);
        events.add(event1);

        // 创建第二个事件
        SyncDataContextEvent event2 = new SyncDataContextEvent();
        JSONObject sourceData2 = new JSONObject();
        sourceData2.put("erp_id", "erp_id_2");
        event2.setSourceData(sourceData2);
        events.add(event2);

        // 创建第三个事件（重复的erp_id）
        SyncDataContextEvent event3 = new SyncDataContextEvent();
        JSONObject sourceData3 = new JSONObject();
        sourceData3.put("erp_id", "erp_id_1");
        event3.setSourceData(sourceData3);
        events.add(event3);

        return events;
    }

    /**
     * 可测试的抽象类实现
     */
    private static class TestableAbstractErpHistoryTaskServiceImpl extends AbstractErpHistoryTaskServiceImpl {

        private boolean checkParamsErrorResult = false;
        private boolean checkTaskValidResult = true;
        private boolean saveTaskSuccessResult = true;
        private boolean editTaskSuccessResult = true;

        public void setCheckParamsErrorResult(boolean result) {
            this.checkParamsErrorResult = result;
        }

        public void setCheckTaskValidResult(boolean result) {
            this.checkTaskValidResult = result;
        }

        public void setSaveTaskSuccessResult(boolean result) {
            this.saveTaskSuccessResult = result;
        }

        public void setEditTaskSuccessResult(boolean result) {
            this.editTaskSuccessResult = result;
        }

        @Override
        public ErpHistoryDataTaskTypeEnum taskType() {
            return ErpHistoryDataTaskTypeEnum.TYPE_IDS;
        }

        @Override
        public boolean checkParamsError(ErpHistoryDataTaskResult task) {
            return checkParamsErrorResult;
        }

        @Override
        public boolean checkTaskValid(ErpHistoryDataTaskEntity task) {
            return checkTaskValidResult;
        }

        @Override
        public boolean saveTaskSuccess(String tenantId, ErpHistoryDataTaskResult task, ErpHistoryDataTaskEntity entity) {
            return saveTaskSuccessResult;
        }

        @Override
        public boolean editTaskSuccess(String tenantId, ErpHistoryDataTaskResult task, ErpHistoryDataTaskEntity entity) {
            return editTaskSuccessResult;
        }

        @Override
        public Result<Void> doTask(String tenantId, ErpHistoryDataTaskEntity task) {
            return Result.newSuccess();
        }
    }
}
