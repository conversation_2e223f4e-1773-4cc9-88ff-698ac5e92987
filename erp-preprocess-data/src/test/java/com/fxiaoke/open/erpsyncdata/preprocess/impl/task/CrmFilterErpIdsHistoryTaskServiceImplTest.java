package com.fxiaoke.open.erpsyncdata.preprocess.impl.task;

import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.open.erpsyncdata.apiproxy.service.ErpDataService;
import com.fxiaoke.open.erpsyncdata.common.constant.EventTypeEnum;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpConnectInfoEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpHistoryDataTaskEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.ErpObjectFieldEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.entity.SyncDataMappingEntity;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpConnectInfoManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.ErpFieldManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncDataMappingManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.SyncLogManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.manager.IdFieldKeyManager;
import com.fxiaoke.open.erpsyncdata.dbproxy.mongo.dao.ErpTempDataDao;
import com.fxiaoke.open.erpsyncdata.preprocess.arg.ErpIdArg;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ErpHistoryDataTaskTypeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.constant.ResultCodeEnum;
import com.fxiaoke.open.erpsyncdata.preprocess.data.SyncDataContextEvent;
import com.fxiaoke.open.erpsyncdata.preprocess.model.CompositeIdExtend;
import com.fxiaoke.open.erpsyncdata.preprocess.model.IdFieldKey;
import com.fxiaoke.open.erpsyncdata.preprocess.result.ErpHistoryDataTaskResult;
import com.fxiaoke.open.erpsyncdata.preprocess.result.base.Result;
import com.fxiaoke.open.erpsyncdata.preprocess.util.PollingDataSpeedRateLimitManager;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CrmFilterErpIdsHistoryTaskServiceImpl Unit Test Class
 * Tests all methods including inherited ones from parent classes
 *
 * <AUTHOR> Assistant
 * @date 2025/06/25
 */
@ExtendWith(MockitoExtension.class)
class CrmFilterErpIdsHistoryTaskServiceImplTest {

    @Mock
    private PollingDataSpeedRateLimitManager pollingDataSpeedRateLimitManager;

    @Mock
    private SyncLogManager syncLogManager;

    @Mock
    private SyncDataMappingManager syncDataMappingManager;

    @Mock
    private ErpDataService erpDataService;

    @Mock
    private IdFieldKeyManager idFieldKeyManager;

    @Mock
    private ErpConnectInfoManager erpConnectInfoManager;

    @Mock
    private ErpFieldManager erpFieldManager;

    @Mock
    private ErpTempDataDao erpTempDataDao;

    @InjectMocks
    private CrmFilterErpIdsHistoryTaskServiceImpl testService;

    private static final String TEST_TENANT_ID = "test_tenant_123";
    private static final String TEST_DC_ID = "test_dc_456";
    private static final Integer TEST_USER_ID = 1001;
    private static final String TEST_TASK_ID = "task_123";
    private static final String TEST_OBJ_API_NAME = "test_obj";
    private static final String TEST_REAL_OBJ_API_NAME = "real_test_obj";

    @BeforeEach
    void setUp() {
        // Inject private fields using reflection
        ReflectionTestUtils.setField(testService, "pollingDataSpeedRateLimitManager", pollingDataSpeedRateLimitManager);
        ReflectionTestUtils.setField(testService, "syncLogManager", syncLogManager);
        ReflectionTestUtils.setField(testService, "syncDataMappingManager", syncDataMappingManager);
        ReflectionTestUtils.setField(testService, "erpDataService", erpDataService);
        ReflectionTestUtils.setField(testService, "idFieldKeyManager", idFieldKeyManager);
        ReflectionTestUtils.setField(testService, "erpConnectInfoManager", erpConnectInfoManager);
        ReflectionTestUtils.setField(testService, "erpFieldManager", erpFieldManager);
        ReflectionTestUtils.setField(testService, "erpTempDataDao", erpTempDataDao);
    }

    /**
     * Test taskType method
     */
    @Test
    void testTaskType() {
        ErpHistoryDataTaskTypeEnum result = testService.taskType();
        assertEquals(ErpHistoryDataTaskTypeEnum.TYPE_ERP_IDS_BY_CRM_FILTER, result);
    }

    /**
     * Test saveTaskSuccess method - success scenario
     */
    @Test
    void testSaveTaskSuccess_Success() {
        // Prepare test data
        ErpHistoryDataTaskResult task = createTestTaskResult();
        ErpHistoryDataTaskEntity entity = createTestTaskEntity();

        // Mock parent class method behavior
        // Since we can't easily mock the parent class methods, we'll test the method signature and basic flow

        // Execute test
        boolean result = testService.saveTaskSuccess(TEST_TENANT_ID, task, entity);

        // Verify result - this will depend on the actual implementation
        // For now, we just verify the method can be called without exception
        assertNotNull(result);
    }

    /**
     * Test editTaskSuccess method - success scenario
     */
    @Test
    void testEditTaskSuccess_Success() {
        // Prepare test data
        ErpHistoryDataTaskResult task = createTestTaskResult();
        ErpHistoryDataTaskEntity entity = createTestTaskEntity();

        // Execute test
        boolean result = testService.editTaskSuccess(TEST_TENANT_ID, task, entity);

        // Verify result
        assertNotNull(result);
    }

    /**
     * Test afterConvert2Vo method
     */
    @Test
    void testAfterConvert2Vo() {
        // Prepare test data
        ErpHistoryDataTaskResult copy = createTestTaskResult();
        copy.setDataIds("id1;id2;id3");
        ErpHistoryDataTaskEntity entity = createTestTaskEntity();

        // Execute test
        assertDoesNotThrow(() -> testService.afterConvert2Vo(copy, entity));

        // Verify that dataIds is set to null
        assertNull(copy.getDataIds());
    }

    /**
     * Test doTask method - success scenario
     */
    @Test
    void testDoTask_Success() {
        // Prepare test data
        ErpHistoryDataTaskEntity task = createTestTaskEntity();

        // Mock dependencies
        when(syncLogManager.initLogId(anyString(), anyString())).thenReturn("test_log_id");

        IdFieldKey mockIdFieldKey = mock(IdFieldKey.class);
        when(idFieldKeyManager.buildIdFieldKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(mockIdFieldKey);

        ErpObjectFieldEntity mockIdField = new ErpObjectFieldEntity();
        mockIdField.setFieldExtendValue("{}");
        when(erpFieldManager.findIdField(anyString(), anyString())).thenReturn(mockIdField);

        doNothing().when(erpTempDataDao).batchUpdateLastSyncTimeByDataIds(anyString(), anyString(), anyString(), anyList());
        doNothing().when(pollingDataSpeedRateLimitManager).acquireQueryErpHistoryData(anyString(), anyLong());

        // Mock ERP data service
        SyncDataContextEvent mockEvent = new SyncDataContextEvent();
        Result<SyncDataContextEvent> mockErpResult = new Result<>(ResultCodeEnum.SUCCESS.getErrCode(), "success", mockEvent);
        when(erpDataService.getErpObjDataById(any(ErpIdArg.class), anyString(), any(IdFieldKey.class)))
                .thenReturn(mockErpResult);

        // Execute test
        Result<Void> result = testService.doTask(TEST_TENANT_ID, task);

        // Verify result
        assertNotNull(result);
        verify(syncLogManager, atLeastOnce()).initLogId(anyString(), anyString());
        verify(idFieldKeyManager).buildIdFieldKey(anyString(), anyString(), anyString(), anyString());
        verify(erpFieldManager).findIdField(anyString(), anyString());
    }

    /**
     * Test getNeedSyncErpIds method through doTask
     */
    @Test
    void testGetNeedSyncErpIds_WithValidMappings() {
        // Prepare test data
        ErpHistoryDataTaskEntity task = createTestTaskEntity();

        // Mock CRM data
        ObjectData crmData1 = new ObjectData();
        crmData1.setId("crm_id_1");
        ObjectData crmData2 = new ObjectData();
        crmData2.setId("crm_id_2");
        List<ObjectData> crmDataList = Arrays.asList(crmData1, crmData2);

        // Mock mapping results
        SyncDataMappingEntity leftMapping = new SyncDataMappingEntity();
        leftMapping.setIsCreated(true);
        leftMapping.setDestDataId("erp_id_1");

        SyncDataMappingEntity rightMapping = new SyncDataMappingEntity();
        rightMapping.setSourceDataId("erp_id_2");

        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), eq("crm_id_1"), anyString(), anyString()))
                .thenReturn(Pair.of(leftMapping, null));
        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), eq("crm_id_2"), anyString(), anyString()))
                .thenReturn(Pair.of(null, rightMapping));

        // Mock other dependencies
        when(syncLogManager.initLogId(anyString(), anyString())).thenReturn("test_log_id");

        IdFieldKey mockIdFieldKey = mock(IdFieldKey.class);
        when(idFieldKeyManager.buildIdFieldKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(mockIdFieldKey);

        ErpObjectFieldEntity mockIdField = new ErpObjectFieldEntity();
        mockIdField.setFieldExtendValue("{}");
        when(erpFieldManager.findIdField(anyString(), anyString())).thenReturn(mockIdField);

        SyncDataContextEvent mockEvent = new SyncDataContextEvent();
        Result<SyncDataContextEvent> mockErpResult = new Result<>(ResultCodeEnum.SUCCESS.getErrCode(), "success", mockEvent);
        when(erpDataService.getErpObjDataById(any(ErpIdArg.class), anyString(), any(IdFieldKey.class)))
                .thenReturn(mockErpResult);

        doNothing().when(erpTempDataDao).batchUpdateLastSyncTimeByDataIds(anyString(), anyString(), anyString(), anyList());
        doNothing().when(pollingDataSpeedRateLimitManager).acquireQueryErpHistoryData(anyString(), anyLong());

        // Execute test
        Result<Void> result = testService.doTask(TEST_TENANT_ID, task);

        // Verify result
        assertNotNull(result);
        verify(syncDataMappingManager, atLeastOnce()).getMapping2Way(anyString(), anyString(), anyString(), anyString(), anyString(), anyString());
    }

    /**
     * Test doTask method - ERP service error scenario
     */
    @Test
    void testDoTask_ErpServiceError() {
        // Prepare test data
        ErpHistoryDataTaskEntity task = createTestTaskEntity();

        // Mock dependencies
        when(syncLogManager.initLogId(anyString(), anyString())).thenReturn("test_log_id");

        IdFieldKey mockIdFieldKey = mock(IdFieldKey.class);
        when(idFieldKeyManager.buildIdFieldKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(mockIdFieldKey);

        ErpObjectFieldEntity mockIdField = new ErpObjectFieldEntity();
        mockIdField.setFieldExtendValue("{}");
        when(erpFieldManager.findIdField(anyString(), anyString())).thenReturn(mockIdField);

        // Mock ERP service error
        Result<SyncDataContextEvent> mockErpResult = new Result<>("500", "ERP service error", null);
        when(erpDataService.getErpObjDataById(any(ErpIdArg.class), anyString(), any(IdFieldKey.class)))
                .thenReturn(mockErpResult);

        // Mock mapping to return some ERP IDs
        SyncDataMappingEntity rightMapping = new SyncDataMappingEntity();
        rightMapping.setSourceDataId("erp_id_1");
        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Pair.of(null, rightMapping));

        // Execute test
        Result<Void> result = testService.doTask(TEST_TENANT_ID, task);

        // Verify error result
        assertNotNull(result);
        assertEquals("500", result.getErrCode());
        verify(erpDataService).getErpObjDataById(any(ErpIdArg.class), anyString(), any(IdFieldKey.class));
    }

    /**
     * Create test ErpHistoryDataTaskResult
     */
    private ErpHistoryDataTaskResult createTestTaskResult() {
        ErpHistoryDataTaskResult task = new ErpHistoryDataTaskResult();
        task.setObjApiName(TEST_OBJ_API_NAME);
        task.setTaskName("Test Task");
        task.setRemark("Test Remark");
        task.setTaskType(ErpHistoryDataTaskTypeEnum.TYPE_ERP_IDS_BY_CRM_FILTER.getStatus());
        task.setExecuteTime(System.currentTimeMillis() + 3600000);
        task.setPriority(1);

        // Add CRM filters
        ErpHistoryDataTaskResult.CrmFilters crmFilter = new ErpHistoryDataTaskResult.CrmFilters();
        crmFilter.setObjectApiName("test_crm_obj");
        task.setCrmFilters(Arrays.asList(crmFilter));

        return task;
    }

    /**
     * Create test ErpHistoryDataTaskEntity
     */
    private ErpHistoryDataTaskEntity createTestTaskEntity() {
        ErpHistoryDataTaskEntity entity = new ErpHistoryDataTaskEntity();
        entity.setId(TEST_TASK_ID);
        entity.setTenantId(TEST_TENANT_ID);
        entity.setDataCenterId(TEST_DC_ID);
        entity.setTaskName("Test Task");
        entity.setObjApiName(TEST_OBJ_API_NAME);
        entity.setRealObjApiName(TEST_REAL_OBJ_API_NAME);
        entity.setTaskType(ErpHistoryDataTaskTypeEnum.TYPE_ERP_IDS_BY_CRM_FILTER.getStatus());
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());
        entity.setExecuteTime(System.currentTimeMillis() + 3600000);
        entity.setTaskNum("TASK_" + System.currentTimeMillis());
        entity.setFilterString("[{\"objectApiName\":\"test_crm_obj\",\"filters\":[]}]");
        return entity;
    }

    /**
     * Test doTask method - no ERP IDs found scenario
     */
    @Test
    void testDoTask_NoErpIdsFound() {
        // Prepare test data
        ErpHistoryDataTaskEntity task = createTestTaskEntity();

        // Mock dependencies
        when(syncLogManager.initLogId(anyString(), anyString())).thenReturn("test_log_id");

        IdFieldKey mockIdFieldKey = mock(IdFieldKey.class);
        when(idFieldKeyManager.buildIdFieldKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(mockIdFieldKey);

        ErpObjectFieldEntity mockIdField = new ErpObjectFieldEntity();
        mockIdField.setFieldExtendValue("{}");
        when(erpFieldManager.findIdField(anyString(), anyString())).thenReturn(mockIdField);

        // Mock mapping to return no valid mappings
        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Pair.of(null, null));

        // Execute test
        Result<Void> result = testService.doTask(TEST_TENANT_ID, task);

        // Verify result
        assertNotNull(result);
        // Should succeed even with no ERP IDs found
        assertEquals(ResultCodeEnum.SUCCESS.getErrCode(), result.getErrCode());

        // Verify that ERP data service is not called when no IDs found
        verify(erpDataService, never()).getErpObjDataById(any(ErpIdArg.class), anyString(), any(IdFieldKey.class));
        verify(erpTempDataDao, never()).batchUpdateLastSyncTimeByDataIds(anyString(), anyString(), anyString(), anyList());
    }

    /**
     * Test doTask method - mapping with non-created left mapping
     */
    @Test
    void testDoTask_NonCreatedLeftMapping() {
        // Prepare test data
        ErpHistoryDataTaskEntity task = createTestTaskEntity();

        // Mock dependencies
        when(syncLogManager.initLogId(anyString(), anyString())).thenReturn("test_log_id");

        IdFieldKey mockIdFieldKey = mock(IdFieldKey.class);
        when(idFieldKeyManager.buildIdFieldKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(mockIdFieldKey);

        ErpObjectFieldEntity mockIdField = new ErpObjectFieldEntity();
        mockIdField.setFieldExtendValue("{}");
        when(erpFieldManager.findIdField(anyString(), anyString())).thenReturn(mockIdField);

        // Mock mapping with non-created left mapping (should be filtered out)
        SyncDataMappingEntity leftMapping = new SyncDataMappingEntity();
        leftMapping.setIsCreated(false); // Not created, should be filtered out
        leftMapping.setDestDataId("erp_id_1");

        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Pair.of(leftMapping, null));

        // Execute test
        Result<Void> result = testService.doTask(TEST_TENANT_ID, task);

        // Verify result
        assertNotNull(result);
        assertEquals(ResultCodeEnum.SUCCESS.getErrCode(), result.getErrCode());

        // Verify that ERP data service is not called for non-created mappings
        verify(erpDataService, never()).getErpObjDataById(any(ErpIdArg.class), anyString(), any(IdFieldKey.class));
    }

    /**
     * Test doTask method - duplicate ERP IDs handling
     */
    @Test
    void testDoTask_DuplicateErpIds() {
        // Prepare test data
        ErpHistoryDataTaskEntity task = createTestTaskEntity();

        // Mock dependencies
        when(syncLogManager.initLogId(anyString(), anyString())).thenReturn("test_log_id");

        IdFieldKey mockIdFieldKey = mock(IdFieldKey.class);
        when(idFieldKeyManager.buildIdFieldKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(mockIdFieldKey);

        ErpObjectFieldEntity mockIdField = new ErpObjectFieldEntity();
        mockIdField.setFieldExtendValue("{}");
        when(erpFieldManager.findIdField(anyString(), anyString())).thenReturn(mockIdField);

        // Mock mappings that return the same ERP ID (should be deduplicated)
        SyncDataMappingEntity rightMapping1 = new SyncDataMappingEntity();
        rightMapping1.setSourceDataId("same_erp_id");

        SyncDataMappingEntity rightMapping2 = new SyncDataMappingEntity();
        rightMapping2.setSourceDataId("same_erp_id");

        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), eq("crm_id_1"), anyString(), anyString()))
                .thenReturn(Pair.of(null, rightMapping1));
        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), eq("crm_id_2"), anyString(), anyString()))
                .thenReturn(Pair.of(null, rightMapping2));

        // Mock ERP service
        SyncDataContextEvent mockEvent = new SyncDataContextEvent();
        Result<SyncDataContextEvent> mockErpResult = new Result<>(ResultCodeEnum.SUCCESS.getErrCode(), "success", mockEvent);
        when(erpDataService.getErpObjDataById(any(ErpIdArg.class), anyString(), any(IdFieldKey.class)))
                .thenReturn(mockErpResult);

        doNothing().when(erpTempDataDao).batchUpdateLastSyncTimeByDataIds(anyString(), anyString(), anyString(), anyList());
        doNothing().when(pollingDataSpeedRateLimitManager).acquireQueryErpHistoryData(anyString(), anyLong());

        // Execute test
        Result<Void> result = testService.doTask(TEST_TENANT_ID, task);

        // Verify result
        assertNotNull(result);
        assertEquals(ResultCodeEnum.SUCCESS.getErrCode(), result.getErrCode());

        // Verify that ERP data service is called only once for duplicate IDs
        verify(erpDataService, times(1)).getErpObjDataById(any(ErpIdArg.class), anyString(), any(IdFieldKey.class));
    }

    /**
     * Test doTask method - task remark update on error
     */
    @Test
    void testDoTask_TaskRemarkUpdateOnError() {
        // Prepare test data
        ErpHistoryDataTaskEntity task = createTestTaskEntity();
        task.setRemark("Initial remark");

        // Mock dependencies
        when(syncLogManager.initLogId(anyString(), anyString())).thenReturn("test_log_id");

        IdFieldKey mockIdFieldKey = mock(IdFieldKey.class);
        when(idFieldKeyManager.buildIdFieldKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(mockIdFieldKey);

        ErpObjectFieldEntity mockIdField = new ErpObjectFieldEntity();
        mockIdField.setFieldExtendValue("{}");
        when(erpFieldManager.findIdField(anyString(), anyString())).thenReturn(mockIdField);

        // Mock mapping to return ERP ID
        SyncDataMappingEntity rightMapping = new SyncDataMappingEntity();
        rightMapping.setSourceDataId("erp_id_1");
        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Pair.of(null, rightMapping));

        // Mock ERP service error
        String errorMessage = "ERP connection failed";
        Result<SyncDataContextEvent> mockErpResult = new Result<>("500", errorMessage, null);
        when(erpDataService.getErpObjDataById(any(ErpIdArg.class), anyString(), any(IdFieldKey.class)))
                .thenReturn(mockErpResult);

        // Execute test
        Result<Void> result = testService.doTask(TEST_TENANT_ID, task);

        // Verify result
        assertNotNull(result);
        assertEquals("500", result.getErrCode());

        // Verify that task remark is updated with error message
        assertTrue(task.getRemark().contains(errorMessage));
    }

    /**
     * Test getNeedSyncErpIds method - mixed mapping scenarios
     */
    @Test
    void testGetNeedSyncErpIds_MixedMappingScenarios() {
        // This test verifies the private getNeedSyncErpIds method through the doTask method
        ErpHistoryDataTaskEntity task = createTestTaskEntity();

        // Mock dependencies
        when(syncLogManager.initLogId(anyString(), anyString())).thenReturn("test_log_id");

        IdFieldKey mockIdFieldKey = mock(IdFieldKey.class);
        when(idFieldKeyManager.buildIdFieldKey(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(mockIdFieldKey);

        ErpObjectFieldEntity mockIdField = new ErpObjectFieldEntity();
        mockIdField.setFieldExtendValue("{}");
        when(erpFieldManager.findIdField(anyString(), anyString())).thenReturn(mockIdField);

        // Mock different mapping scenarios
        // 1. Valid right mapping
        SyncDataMappingEntity rightMapping = new SyncDataMappingEntity();
        rightMapping.setSourceDataId("erp_id_from_right");

        // 2. Valid left mapping (created)
        SyncDataMappingEntity leftMappingCreated = new SyncDataMappingEntity();
        leftMappingCreated.setIsCreated(true);
        leftMappingCreated.setDestDataId("erp_id_from_left_created");

        // 3. Invalid left mapping (not created)
        SyncDataMappingEntity leftMappingNotCreated = new SyncDataMappingEntity();
        leftMappingNotCreated.setIsCreated(false);
        leftMappingNotCreated.setDestDataId("erp_id_from_left_not_created");

        // 4. No mapping
        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), eq("crm_id_1"), anyString(), anyString()))
                .thenReturn(Pair.of(null, rightMapping));
        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), eq("crm_id_2"), anyString(), anyString()))
                .thenReturn(Pair.of(leftMappingCreated, null));
        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), eq("crm_id_3"), anyString(), anyString()))
                .thenReturn(Pair.of(leftMappingNotCreated, null));
        when(syncDataMappingManager.getMapping2Way(anyString(), anyString(), anyString(), eq("crm_id_4"), anyString(), anyString()))
                .thenReturn(Pair.of(null, null));

        // Mock ERP service
        SyncDataContextEvent mockEvent = new SyncDataContextEvent();
        Result<SyncDataContextEvent> mockErpResult = new Result<>(ResultCodeEnum.SUCCESS.getErrCode(), "success", mockEvent);
        when(erpDataService.getErpObjDataById(any(ErpIdArg.class), anyString(), any(IdFieldKey.class)))
                .thenReturn(mockErpResult);

        doNothing().when(erpTempDataDao).batchUpdateLastSyncTimeByDataIds(anyString(), anyString(), anyString(), anyList());
        doNothing().when(pollingDataSpeedRateLimitManager).acquireQueryErpHistoryData(anyString(), anyLong());

        // Execute test
        Result<Void> result = testService.doTask(TEST_TENANT_ID, task);

        // Verify result
        assertNotNull(result);
        assertEquals(ResultCodeEnum.SUCCESS.getErrCode(), result.getErrCode());

        // Verify that ERP data service is called for valid mappings only
        // Should be called twice: once for right mapping and once for created left mapping
        verify(erpDataService, times(2)).getErpObjDataById(any(ErpIdArg.class), anyString(), any(IdFieldKey.class));
    }
}
